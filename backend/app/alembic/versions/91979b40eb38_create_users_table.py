"""create users table

Revision ID: 91979b40eb38
Revises: 
Create Date: 2020-03-23 14:53:53.101322

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "91979b40eb38"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "user",
        sa.<PERSON>umn("id", sa.Integer, primary_key=True),
        sa.<PERSON>umn("email", sa.String(50), nullable=False),
        sa.<PERSON>umn("first_name", sa.String(100)),
        sa.<PERSON>umn("last_name", sa.String(100)),
        sa.Column("address", sa.String(100)),
        sa.Column("hashed_password", sa.String(100), nullable=False),
        sa.<PERSON>umn("is_active", sa.Bo<PERSON>, nullable=False),
        sa.<PERSON>umn("is_superuser", sa.Bo<PERSON>, nullable=False),
    )


def downgrade():
    op.drop_table("user")
