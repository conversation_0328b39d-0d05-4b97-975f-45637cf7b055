version: '3.7'
services:
  nginx:
    image: nginx:1.17
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
    ports:
      - 9000:80
    depends_on:
      - backend
      - frontend

  redis:
    image: redis
    ports:
      - 6379:6379

  postgres:
    image: postgres:12
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - '5432:5432'
    volumes:
      - db-data:/var/lib/postgresql/data:cached

  worker:
    build:
      context: backend
      dockerfile: Dockerfile
    command: celery --app app.tasks worker --loglevel=DEBUG -Q main-queue -c 1

  flower:  
    image: mher/flower
    command: celery flower --broker=redis://redis:6379/0 --port=5555
    ports:  
        - 5555:5555
    depends_on:
      - "redis"

  backend:
    build:
      context: backend
      dockerfile: Dockerfile
    command: python app/main.py
    tty: true
    volumes:
      - ./backend:/app/:cached
      - ./.docker/.ipython:/root/.ipython:cached
    environment:
      PYTHONPATH: .
      DATABASE_URL: '********************************************/postgres'
    depends_on:
      - "postgres"

  frontend:
    build:
      context: frontend
      dockerfile: Dockerfile
    stdin_open: true
    volumes:
      - './frontend:/app:cached'
      - './frontend/node_modules:/app/node_modules:cached'
    environment:
      - NODE_ENV=development


volumes:
  db-data:
